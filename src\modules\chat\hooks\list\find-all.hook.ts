"use client";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { IChatKnowledgeDto } from "../../types/dtos/find-all-knowledge.dto";

export const useFindAllChatKnowledge = (params: IPaginationParameters) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: chatKeys.list({ ...params }),
		queryFn: () => createGetRequest<IResponsePaginated<IChatKnowledgeDto>>(CHAT_ENDPOINTS.FIND_ALL_KNOWLEDGE(params)),
		enabled: canRead("all"),
		placeholderData: keepPreviousData,
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
