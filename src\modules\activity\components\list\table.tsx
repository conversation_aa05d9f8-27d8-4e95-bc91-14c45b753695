import { pathService } from "@/config/path-manager/service";
import { MainHeaderComponent } from "@/shared/components/custom/admin-header";
import { GenericEmptyState } from "@/shared/components/custom/empty";
import { ComponentType, useState } from "react";
import { ACTIVITY_SUBJECTS } from "../../constants/subjects";
import { useTableActivity } from "../../hooks/list/table-activity.hook";
import { ActivityDesktopTableView } from "./desktop-table-view";
import { ActivityMobileTableView } from "./mobile-table-view";

interface ITableActivityProps {
	onOpenModal: () => void;
}

export const TableActivity = ({ onOpenModal }: ITableActivityProps) => {
	const [searchTerm, setSearchTerm] = useState<string>("");
	const item = pathService.getItemById("activity");
	const Icon = item?.icon as ComponentType<unknown> | undefined;

	const { data, isLoading, pagination, isEmpty, isMobile, error, hasError, pageSize, handlePageSizeChange, setCurrentPage } = useTableActivity({
		searchTerm,
	});

	return (
		<main className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 px-4 py-1 md:px-2">
			<MainHeaderComponent
				icon={Icon ? <Icon /> : undefined}
				title="Atividades"
				description="Registros de atividades do sistema"
				total={pagination?.totalItems}
				search={searchTerm}
				onSearchChange={setSearchTerm}
				onAdd={onOpenModal}
				searchPlaceholder="Buscar atividades..."
				totalLabelSingular="atividade"
				totalLabelPlural="atividades"
				subject={ACTIVITY_SUBJECTS.ACTIVITY}
			/>
			<div className="h-full">
				{isEmpty && !isLoading ? (
					<GenericEmptyState
						buttonText="Adicionar atividade"
						description="Ainda não há atividades cadastradas."
						onAction={() => onOpenModal()}
						title="Nenhuma atividade encontrada"
						subject={ACTIVITY_SUBJECTS.ACTIVITY}
					/>
				) : isMobile ? (
					<ActivityMobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				) : (
					<ActivityDesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						pagination={pagination ?? null}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
						pageSize={pageSize}
					/>
				)}
			</div>
		</main>
	);
};
