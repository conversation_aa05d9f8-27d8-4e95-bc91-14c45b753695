import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { Pagination } from "@/shared/components/custom/pagination";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { ComponentIcon } from "lucide-react";
import { IProductTypesDto } from "../../types/find-all.dto";
import { ProductTypeCardMobile } from "./card-mobile";

interface MobileTableViewProps {
	data: IProductTypesDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	searchTerm: string;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
}

export const ProductTypeMobileTableView = ({
	data,
	isLoading,
	hasError,
	error,
	searchTerm,
	pagination,
	onPageChange,
	onPageSizeChange,
}: MobileTableViewProps) => {
	const renderContent = () => {
		if (hasError) {
			return <div className="text-destructive h-24 text-center">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>;
		}

		if (isLoading) return <Skeleton className="mx-auto h-6 w-48" />;
		if (data && data.length) return data.map(item => <ProductTypeCardMobile key={item.id} item={item} />);

		return (
			<EmptyStateTable
				searchTerm={searchTerm}
				icon={<ComponentIcon />}
				title="Nenhum tipo de produto encontrado"
				description={searchTerm ? "Nenhum tipo de produto corresponde ao termo pesquisado." : "Ainda não há tipos de produtos cadastrados."}
				tip="Você pode tentar pesquisar por outros termos ou adicionar um novo tipo de produto."
			/>
		);
	};

	return (
		<div className="space-y-4">
			{renderContent()}
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
