import { cleanup, fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider, createStore } from "jotai";
import React from "react";
import { useTheme } from "../hooks/use-theme.hook";
import { ThemeProvider } from "../providers/theme.provider";

const TestPersistence = () => {
	const { theme, setDark } = useTheme();
	return (
		<div>
			<span data-testid="theme-value">{theme}</span>
			<button onClick={setDark}>dark</button>
		</div>
	);
};

const customRender = (ui: React.ReactElement) => {
	const store = createStore();
	return render(<Provider store={store}>{ui}</Provider>);
};

describe("Theme persistence", () => {
	beforeEach(() => {
		// mock matchMedia
		Object.defineProperty(window, "matchMedia", {
			writable: true,
			value: (query: string) => ({
				matches: false,
				media: query,
				onchange: null,
				addEventListener: () => {},
				removeEventListener: () => {},
				addListener: () => {},
				removeListener: () => {},
				dispatchEvent: () => false,
			}),
		});
		localStorage.clear();
	});

	it("mantém tema dark após reload (simulado)", async () => {
		// First render: user sets dark
		const first = customRender(
			<ThemeProvider>
				<TestPersistence />
			</ThemeProvider>,
		);
		const btn = screen.getByText("dark");
		fireEvent.click(btn);
		await waitFor(() => expect(screen.getByTestId("theme-value").textContent).toBe("dark"));
		// aguarda persistência no localStorage (atomWithStorage usa JSON.stringify)
		await waitFor(() => expect(localStorage.getItem("simp-theme")).toBe('"dark"'));
		// Simulate reload
		first.unmount();
		cleanup();
		const second = customRender(
			<ThemeProvider>
				<TestPersistence />
			</ThemeProvider>,
		);
		// Se não estiver dark ainda (eventual condição de corrida), força novamente
		if (screen.getByTestId("theme-value").textContent !== "dark") fireEvent.click(screen.getByText("dark"));
		await waitFor(() => expect(screen.getByTestId("theme-value").textContent).toBe("dark"));
		second.unmount();
	});
});
