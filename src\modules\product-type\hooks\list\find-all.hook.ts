"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";
import { IProductTypesDto } from "../../types/find-all.dto";

export const useFindAllProductTypes = (params: IPaginationParameters) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: productTypeKeys.list({
			...params,
		}),
		queryFn: () => createGetRequest<IResponsePaginated<IProductTypesDto>>(PRODUCT_TYPE_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE),
	});

	const isNoDataFound = (!data?.success && data?.status === 404) || (data?.success && data?.data.totalItems === 0);

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
