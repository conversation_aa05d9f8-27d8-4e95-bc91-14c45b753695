"use client";

import { useQuery } from "@tanstack/react-query";
import { usePermissions } from "../../../../shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IPaginationParameters } from "../../../../shared/types/pagination/types";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";
import { IComponentTypesDto } from "../../types/find-all.dto";

export const useFindAllComponentTypes = (params: IPaginationParameters) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: componentsKeys.list({
			...params,
		}),
		queryFn: () => createGetRequest<IResponsePaginated<IComponentTypesDto>>(COMPONENTS_TYPES_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE),
	});

	const isNoDataFound = (!data?.success && data?.status === 404) || (data?.success && data?.data.data.length === 0);

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
