import { ProtectedComponent } from "@/shared/components/auth/protected";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Trash } from "lucide-react";
import { IComponentTypesDto } from "../../types/find-all.dto";
import { DeleteComponentTypeModal } from "../delete/modal";

interface IComponentTypeCardMobileProps {
	item: IComponentTypesDto;
}

export const ComponentTypeCardMobile = ({ item }: IComponentTypeCardMobileProps) => {
	const modals = {
		edit: useModal(),
		delete: useModal(),
	};

	return (
		<Card className="bg-card relative border shadow-sm transition-shadow hover:shadow-md">
			<CardContent>
				<div className="mb-3 flex items-start justify-between">
					<h3 className="text-card-foreground mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">{item.name}</h3>
				</div>

				<Separator className="my-4" />

				<div className="flex gap-2">
					<ProtectedComponent action="manage" subject="all">
						<Button
							size="sm"
							variant="ghost"
							className="border-destructive bg-destructive/10 text-destructive hover:bg-destructive/20 hover:text-destructive h-8 flex-1 border px-2 text-xs"
							onClick={modals.delete.toggleModal}
						>
							<Trash className="h-3 w-3" />
							<span>Excluir</span>
						</Button>
					</ProtectedComponent>
				</div>
			</CardContent>

			<DeleteComponentTypeModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} name={item.name} id={item.id} />
		</Card>
	);
};
